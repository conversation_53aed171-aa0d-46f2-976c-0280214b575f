import { cn } from '@/lib/utils';

interface AdminCardProps {
  className?: string;
  children: React.ReactNode;
}

export function AdminCard({ className, children }: AdminCardProps) {
  return (
    <div
      className={cn(
        "bg-card text-card-foreground rounded-lg border border-border p-6 transition-all hover:bg-muted/50 shadow-sm",
        className
      )}
    >
      {children}
    </div>
  );
}