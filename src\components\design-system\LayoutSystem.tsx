/**
 * Layout System for Festival Family
 * Eliminates background layering issues and provides consistent page structure
 * Single source of truth for all layout patterns
 */

import React from 'react';
import { motion } from 'framer-motion';

// ===== PAGE LAYOUT COMPONENT =====
// Eliminates the "boxed" appearance by providing single background layer
interface PageLayoutProps {
  variant?: 'default' | 'auth' | 'admin';
  children: React.ReactNode;
  className?: string;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  variant = 'default',
  children,
  className = ''
}) => {
  const variantClasses = {
    default: 'min-h-screen bg-dark-gradient',
    auth: 'min-h-screen bg-dark-gradient',
    admin: 'min-h-screen bg-primary'
  };

  return (
    <div className={`${variantClasses[variant]} ${className}`}>
      {children}
    </div>
  );
};

// ===== CONTENT CONTAINER =====
// Provides consistent content width and spacing
interface ContentContainerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
}

export const ContentContainer: React.FC<ContentContainerProps> = ({
  size = 'lg',
  padding = 'md',
  children,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-4',
    md: 'px-4 py-8 sm:px-6 lg:px-8',
    lg: 'px-4 py-12 sm:px-6 lg:px-8'
  };

  return (
    <div className={`mx-auto ${sizeClasses[size]} ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

// ===== SECTION LAYOUT =====
// Provides consistent section spacing without background conflicts
interface SectionLayoutProps {
  spacing?: 'none' | 'sm' | 'md' | 'lg';
  background?: 'none' | 'subtle' | 'card';
  children: React.ReactNode;
  className?: string;
}

export const SectionLayout: React.FC<SectionLayoutProps> = ({
  spacing = 'md',
  background = 'none',
  children,
  className = ''
}) => {
  const spacingClasses = {
    none: '',
    sm: 'py-4',
    md: 'py-8',
    lg: 'py-16'
  };

  const backgroundClasses = {
    none: '',
    subtle: 'bg-card bg-opacity-50',
    card: 'bg-card component-base'
  };

  return (
    <section className={`${spacingClasses[spacing]} ${backgroundClasses[background]} ${className}`}>
      {children}
    </section>
  );
};

// ===== GRID LAYOUT =====
// Responsive grid system using design tokens
interface GridLayoutProps {
  cols?: 1 | 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
}

export const GridLayout: React.FC<GridLayoutProps> = ({
  cols = 3,
  gap = 'md',
  children,
  className = ''
}) => {
  const colClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6'
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  return (
    <div className={`grid ${colClasses[cols]} ${gapClasses[gap]} ${className}`}>
      {children}
    </div>
  );
};

// ===== FLEX LAYOUT =====
// Flexible layout component
interface FlexLayoutProps {
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
  gap?: 'sm' | 'md' | 'lg';
  wrap?: boolean;
  children: React.ReactNode;
  className?: string;
}

export const FlexLayout: React.FC<FlexLayoutProps> = ({
  direction = 'row',
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  children,
  className = ''
}) => {
  const directionClass = direction === 'row' ? 'flex-row' : 'flex-col';
  const alignClass = `items-${align}`;
  const justifyClass = `justify-${justify}`;
  const gapClass = gap === 'sm' ? 'gap-2' : gap === 'md' ? 'gap-4' : 'gap-6';
  const wrapClass = wrap ? 'flex-wrap' : '';

  return (
    <div className={`flex ${directionClass} ${alignClass} ${justifyClass} ${gapClass} ${wrapClass} ${className}`}>
      {children}
    </div>
  );
};

// ===== HEADER LAYOUT =====
// Consistent header layout without background conflicts
interface HeaderLayoutProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  className?: string;
}

export const HeaderLayout: React.FC<HeaderLayoutProps> = ({
  title,
  subtitle,
  actions,
  className = ''
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8 ${className}`}
    >
      <div>
        <h1 className="text-3xl font-bold text-primary-dark">{title}</h1>
        {subtitle && (
          <p className="text-secondary-dark mt-2">{subtitle}</p>
        )}
      </div>
      {actions && (
        <div className="flex items-center gap-3">
          {actions}
        </div>
      )}
    </motion.div>
  );
};

// ===== CARD GRID =====
// Specialized grid for cards with consistent spacing
interface CardGridProps {
  children: React.ReactNode;
  className?: string;
}

export const CardGrid: React.FC<CardGridProps> = ({
  children,
  className = ''
}) => {
  return (
    <GridLayout cols={3} gap="md" className={className}>
      {children}
    </GridLayout>
  );
};

// ===== MAIN CONTENT AREA =====
// Eliminates background layering by providing single content area
interface MainContentProps {
  children: React.ReactNode;
  className?: string;
}

export const MainContent: React.FC<MainContentProps> = ({
  children,
  className = ''
}) => {
  return (
    <main className={`flex-1 ${className}`}>
      <ContentContainer>
        {children}
      </ContentContainer>
    </main>
  );
};

// ===== COMPLETE PAGE WRAPPER =====
// Single source of truth for page structure
interface PageWrapperProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  variant?: 'default' | 'auth' | 'admin';
  className?: string;
}

export const PageWrapper: React.FC<PageWrapperProps> = ({
  title,
  subtitle,
  actions,
  children,
  variant = 'default',
  className = ''
}) => {
  return (
    <PageLayout variant={variant} className={className}>
      <MainContent>
        <HeaderLayout title={title} subtitle={subtitle} actions={actions} />
        {children}
      </MainContent>
    </PageLayout>
  );
};
