// Festival Family Design System
// Single source of truth for all UI components and layouts

// ===== UNIFIED COMPONENTS =====
// Single source of truth components - use these instead of duplicates
export {
  UnifiedButton,
  UnifiedCard,
  UnifiedBadge,
  UnifiedIconButton,
  UnifiedContainer,
  UnifiedAnnouncement
} from './UnifiedComponents';

// ===== LAYOUT SYSTEM =====
// Eliminates background layering issues and provides consistent structure
export {
  PageLayout,
  ContentContainer,
  SectionLayout,
  GridLayout,
  FlexLayout,
  HeaderLayout,
  CardGrid,
  MainContent,
  PageWrapper
} from './LayoutSystem';

// ===== LEGACY MODAL SYSTEM =====
// TODO: Migrate to UnifiedComponents
export { ResponsiveModal } from './ResponsiveModal';
export {
  ModalContent,
  ModalSection,
  ModalMetadata,
  ModalDescription,
  ModalTags,
  ModalActions,
  ModalImage,
  ModalStats,
  ModalFooter,
} from './ModalComponents';

// ===== TYPES =====
export type ResponsiveModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';
export type ComponentVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type ComponentSize = 'sm' | 'md' | 'lg';
export type PriorityLevel = 'high' | 'medium' | 'low';

// ===== CONSTANTS =====
export const DESIGN_SYSTEM_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
} as const;

// Spacing utilities (will be added in next priority)
// Button system (will be added in next priority)
// Navigation system (will be added in next priority)
