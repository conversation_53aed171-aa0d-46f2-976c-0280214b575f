import { Outlet, useNavigate } from 'react-router-dom';
import { useProfile } from '@/hooks/useProfile';
import AdminNav from '@/components/admin/AdminNav';
import { UserNav } from '@/components/admin/UserNav';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { LoadingState } from '@/components/admin/LoadingState';
import { Button } from '@/components/ui/button';

import { useState, startTransition } from 'react';
import { Menu, X, User } from 'lucide-react';

const AdminLayout = () => {
  const { profile, isLoading: profileLoading } = useProfile();
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (profileLoading) {
    return <LoadingState />;
  }

  if (!isAdmin) {
    return <div className="p-8 text-center">Access Denied</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <button
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                setSidebarOpen(false);
              }
            }}
            aria-label="Close sidebar"
          />
        )}

        {/* Sidebar */}
        <div className={`
          fixed lg:static inset-y-0 left-0 z-50 w-64 min-h-screen
          bg-card border-r border-border
          transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}>
          <div className="flex items-center justify-between p-6">
            <h1 className="text-xl font-bold text-foreground">
              Admin Panel
            </h1>
            {/* Close button for mobile */}
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <AdminNav onNavigate={() => setSidebarOpen(false)} />
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          <header className="sticky top-0 z-10 h-16 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center justify-between h-full px-6">
              <div className="flex items-center space-x-4">
                {/* Mobile menu button */}
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted"
                >
                  <Menu className="h-5 w-5" />
                </button>
                <div className="text-sm text-muted-foreground">
                  Welcome back, {profile?.full_name ?? profile?.username}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Return to User View button */}
                <Button
                  onClick={() => {
                    startTransition(() => {
                      navigate('/');
                    });
                  }}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <User className="h-4 w-4" />
                  <span className="hidden sm:inline">Return to User View</span>
                  <span className="sm:hidden">User View</span>
                </Button>

                <UserNav />
              </div>
            </div>
          </header>

          <main className="p-4 lg:p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
