import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';

/**
 * Dark Mode Toggle Button Component
 * 
 * Provides a simple toggle button to switch between light and dark themes.
 * Uses the ThemeContext to manage theme state and applies the dark class
 * to the document root for CSS variable switching.
 */
export const DarkModeToggle: React.FC = () => {
  const { mode, toggleMode } = useTheme();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleMode}
      className="h-8 w-8 p-0 text-white/70 hover:text-white hover:bg-white/10 transition-colors"
      title={mode === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
      aria-label={mode === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {mode === 'dark' ? (
        <Sun className="h-4 w-4" />
      ) : (
        <Moon className="h-4 w-4" />
      )}
    </Button>
  );
};

export default DarkModeToggle;
