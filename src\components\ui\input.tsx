import * as React from "react"
import { motion, MotionProps } from "framer-motion"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

// Separate MotionProps from standard InputHTMLAttributes
type MotionInputProps = InputProps & MotionProps;

const Input = React.forwardRef<HTMLInputElement, MotionInputProps>(
  ({ className, type, ...props }, ref) => {
    // Cast props to any to bypass the complex type intersection issue temporarily.
    return (
      <motion.input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          // Emergency fix for text visibility: ensure dark text on light background
          "text-foreground",
          className
        )}
        ref={ref}
        {...(props as any)} // Use type assertion as a workaround
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
