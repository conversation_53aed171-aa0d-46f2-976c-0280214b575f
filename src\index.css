/* Font imports are now handled in the main.tsx file */

/* Import Festival Family 2025 Design System */
@import './styles/design-tokens.css';

/* Import local styles */
@import './styles/container.css';
@import './styles/utilities.css';

/* Import Tailwind */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  :root {
    /* === FESTIVAL FAMILY DESIGN SYSTEM === */
    /* Consolidated CSS variables using shadcn/ui naming with Festival Family colors */

    /* Light theme - Festival Family colors mapped to shadcn/ui semantics */
    --background: 0 0% 100%; /* Pure white for main background */
    --foreground: 30 10% 11%; /* Dark brown-gray for main text (from design tokens) */

    --card: 0 0% 100%; /* White cards */
    --card-foreground: 30 10% 11%; /* Dark text on cards */

    --popover: 0 0% 100%; /* White popovers */
    --popover-foreground: 30 10% 11%; /* Dark text on popovers */

    --primary: 262 83% 58%; /* Festival purple (--color-accent-purple-500) */
    --primary-foreground: 0 0% 100%; /* White text on purple */

    --secondary: 25 25% 69%; /* Mocha Mousse (--color-primary-500) */
    --secondary-foreground: 30 10% 11%; /* Dark text on mocha */

    --muted: 60 9% 98%; /* Very light warm gray */
    --muted-foreground: 215 16% 47%; /* Medium gray for muted text */

    --accent: 24 100% 50%; /* Festival orange (--color-accent-orange-500) */
    --accent-foreground: 0 0% 100%; /* White text on orange */

    --destructive: 0 84% 60%; /* Error red */
    --destructive-foreground: 0 0% 100%; /* White text on red */

    --border: 20 6% 90%; /* Light warm border */
    --input: 20 6% 90%; /* Light warm input border */
    --ring: 262 83% 58%; /* Purple focus ring */

    --radius: 0.5rem; /* Border radius */
  }

  .dark {
    /* Dark theme - Festival Family dark colors */
    --background: 262 45% 8%; /* Deep purple-navy (--color-bg-primary) */
    --foreground: 0 0% 100%; /* White text for dark backgrounds */

    --card: 262 35% 14%; /* Slightly lighter purple for cards (--color-bg-card) */
    --card-foreground: 0 0% 100%; /* White text on dark cards */

    --popover: 262 35% 14%; /* Dark purple popovers */
    --popover-foreground: 0 0% 100%; /* White text on dark popovers */

    --primary: 262 83% 58%; /* Same festival purple */
    --primary-foreground: 0 0% 100%; /* White text on purple */

    --secondary: 262 35% 14%; /* Dark purple secondary */
    --secondary-foreground: 0 0% 100%; /* White text on dark secondary */

    --muted: 262 25% 20%; /* Muted dark purple */
    --muted-foreground: 220 9% 46%; /* Light gray for muted text */

    --accent: 24 100% 50%; /* Same festival orange */
    --accent-foreground: 0 0% 100%; /* White text on orange */

    --destructive: 0 84% 60%; /* Same error red */
    --destructive-foreground: 0 0% 100%; /* White text on red */

    --border: 262 25% 20%; /* Dark purple border */
    --input: 262 25% 20%; /* Dark purple input border */
    --ring: 262 83% 58%; /* Purple focus ring */
  }
}

@layer base {
  * {
    @apply border-white/10;
  }

  html {
    @apply antialiased;
  }

  body {
    @apply min-h-screen;
    font-family: 'Manrope Variable', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    /* Use consolidated CSS variables for consistent theming */
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    /* Remove conflicting gradient and animation for now - can be re-added later */
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Outfit Variable', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    @apply font-bold;
  }
}

/* Gradient animation */
@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Touch target sizes for mobile */
.touch-target {
  @apply min-w-[44px] min-h-[44px];
}

/* Glassmorphism effects */
:root {
  --glass-bg-color: rgba(255, 255, 255, 0.1);
}

.glass {
  @apply bg-white/10 backdrop-blur-md;
  @apply border border-white/20;
}

.glass-dark {
  @apply bg-black/40 backdrop-blur-md;
  @apply border border-white/10;
}
