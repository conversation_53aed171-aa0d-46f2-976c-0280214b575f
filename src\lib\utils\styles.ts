import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// DEPRECATED - Use design tokens and UnifiedComponents instead
export const glassmorphismClasses = {
  card: 'DEPRECATED - Use UnifiedCard component',
  button: 'DEPRECATED - Use UnifiedButton component',
  buttonOutline: 'DEPRECATED - Use UnifiedButton variant="outline"',
  input: 'DEPRECATED - Use design tokens',
  nav: 'DEPRECATED - Use design tokens',
  header: 'DEPRECATED - Use design tokens',
  chip: 'DEPRECATED - Use UnifiedBadge component',
  background: 'DEPRECATED - Use bg-dark-gradient class',
}

export const gradientClasses = {
  background: 'bg-gradient-to-br from-midnight-purple to-electric-violet',
  card: 'bg-gradient-to-br from-white from-opacity-10 to-white to-opacity-5',
  button: 'bg-gradient-to-r from-electric-violet to-neon-pink',
  highlight: 'bg-gradient-to-r from-aqua-blue to-bright-yellow',
  glass: 'bg-midnight-purple bg-opacity-80 backdrop-blur-xl',
  animated: 'animate-gradient-x bg-gradient-to-r from-electric-violet via-neon-pink to-aqua-blue'
}

export const shadowClasses = {
  sm: 'shadow-glow-primary shadow-opacity-10',
  md: 'shadow-glow-primary shadow-opacity-20',
  lg: 'shadow-glow-primary shadow-opacity-30',
  xl: 'shadow-glow-primary shadow-opacity-40',
}

export const transitionClasses = {
  fast: 'transition-all duration-150 ease-in-out',
  normal: 'transition-all duration-300 ease-in-out',
  slow: 'transition-all duration-500 ease-in-out',
}

export const hoverClasses = {
  scale: 'hover:scale-105 transition-transform duration-300',
  lift: 'hover:-translate-y-1 transition-transform duration-300',
  glow: 'hover:shadow-glow-primary transition-shadow duration-300',
  pulse: 'hover:animate-pulse',
}

export const animationClasses = {
  fadeIn: 'animate-fadeIn',
  slideUp: 'animate-slideUp',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
  spin: 'animate-spin',
  shimmer: 'animate-shimmer',
}

export const layoutClasses = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-8 sm:py-12',
  grid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
}

export const textClasses = {
  title: 'text-4xl font-bold',
  subtitle: 'text-2xl font-semibold',
  heading: 'text-2xl font-bold',
  subheading: 'text-lg font-medium',
  body: 'text-base',
  small: 'text-sm',
  active: 'text-purple-300',
  inactive: 'hover:text-white',
  admin: 'text-foreground font-medium', // ← FIXED: Use theme-aware foreground color
  // New improved text classes using CSS variables
  primaryDark: 'text-white',
  secondaryDark: 'text-gray-200',
  mutedDark: 'text-gray-400',
  primaryLight: 'text-gray-900',
  secondaryLight: 'text-gray-600',
  mutedLight: 'text-gray-500'
}

export const buttonClasses = {
  primary: 'px-4 py-2 rounded-lg bg-gradient-to-r from-electric-violet to-neon-pink text-white font-medium hover:shadow-glow-primary transition-all duration-300',
  secondary: 'px-4 py-2 rounded-lg bg-white bg-opacity-10 hover:bg-white hover:bg-opacity-20 text-white font-medium transition-all duration-300',
  nav: 'px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10 text-white font-medium transition-all duration-300 flex items-center space-x-2',
  icon: 'p-2 rounded-full hover:bg-white hover:bg-opacity-10 text-white transition-all duration-300',
  admin: 'px-4 py-2 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-all duration-300' // ← FIXED: Professional admin button styling
}

export const formClasses = {
  label: 'block text-sm font-medium text-slate-gray dark:text-soft-gray',
  input: 'mt-1 block w-full rounded-lg bg-white bg-opacity-5 border border-electric-violet border-opacity-20 focus:border-aqua-blue focus:ring-2 focus:ring-aqua-blue focus:ring-opacity-50 transition-colors',
  select: 'mt-1 block w-full rounded-lg bg-white bg-opacity-5 border border-electric-violet border-opacity-20 focus:border-aqua-blue focus:ring-2 focus:ring-aqua-blue focus:ring-opacity-50 transition-colors',
  checkbox: 'rounded border border-electric-violet border-opacity-20 text-electric-violet focus:ring-aqua-blue focus:ring-opacity-50 transition-colors',
}

export const adminClasses = {
  badge: 'px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-full', // ← FIXED: Professional badge styling
  icon: 'text-muted-foreground', // ← FIXED: Theme-aware icon color
  nav: 'bg-muted/50 hover:bg-muted', // ← FIXED: Professional navigation styling
  glow: 'shadow-sm' // ← FIXED: Subtle professional shadow
}

export const loadingClasses = {
  spinner: 'animate-spin rounded-full border-t-2 border-b-2 border-primary',
  pulse: 'animate-pulse bg-white bg-opacity-10',
  shimmer: 'animate-shimmer bg-gradient-to-r from-transparent via-white via-opacity-10 to-transparent',
  dots: 'animate-bounce-delayed flex space-x-1'
}