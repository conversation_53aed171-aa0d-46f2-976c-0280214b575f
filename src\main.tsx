import React, { lazy, Suspense } from 'react'
import ReactDOM from 'react-dom/client'
import {
  create<PERSON>rowser<PERSON><PERSON>er,
  RouterProvider,
  Navigate
} from 'react-router-dom'

// Import fonts directly
import '@fontsource/manrope/400.css'
import '@fontsource/manrope/500.css'
import '@fontsource/manrope/700.css'
import '@fontsource/outfit/400.css'
import '@fontsource/outfit/500.css'
import '@fontsource/outfit/600.css'
import '@fontsource/outfit/700.css'

// Import minimal styles
import './index.css'

// Initialize error monitoring and analytics
import { initSentry } from './lib/sentry'
import { Analytics } from '@vercel/analytics/react'
import { initializeMobileOptimizations } from './utils/mobileOptimizationInit'

// Initialize core systems
initSentry()

// Initialize mobile optimizations
initializeMobileOptimizations().then(() => {
  console.log('🎉 Mobile optimizations ready');
}).catch(error => {
  console.warn('⚠️ Mobile optimization initialization failed:', error);
});

// Import components
import ConsolidatedAuthProvider from './providers/ConsolidatedAuthProvider'
import QueryProvider from './providers/QueryProvider'
import GlobalErrorBoundary from './components/error/GlobalErrorBoundary'
import { SecurityProvider } from './components/security/SecurityProvider'
import { ThemeProvider } from './contexts/ThemeContext'
import LoadingSpinner from './components/ui/LoadingSpinner'
import ConnectionStatus from './components/debug/ConnectionStatus'

// Lazy load only essential pages
const SimpleAuth = lazy(() => import('./pages/SimpleAuth'));
const SmartHome = lazy(() => import('./pages/SmartHome'));
const NotFound = lazy(() => import('./pages/NotFound'));
const AuthCallback = lazy(() => import('./pages/AuthCallback'));

// Debug pages - only load in development
const SupabaseTest = import.meta.env.DEV ? lazy(() => import('./pages/debug/SupabaseTest')) : null;
const ClientTest = import.meta.env.DEV ? lazy(() => import('./pages/debug/ClientTest')) : null;
const ErrorDemo = import.meta.env.DEV ? lazy(() => import('./pages/debug/ErrorDemo')) : null;
const RetryDemo = import.meta.env.DEV ? lazy(() => import('./pages/debug/RetryDemo')) : null;

// Import the ProtectedRoute component
import ProtectedRoute from './components/auth/ProtectedRoute';

// Lazy load additional pages
const Profile = lazy(() => import('./pages/Profile'));
const Activities = lazy(() => import('./pages/Activities'));
const FamHub = lazy(() => import('./pages/FamHub'));
const Discover = lazy(() => import('./pages/Discover'));
const Emergency = lazy(() => import('./pages/Emergency'));
const Help = lazy(() => import('./pages/Help'));
const Resources = lazy(() => import('./pages/Resources'));

// Import the AppLayout component
const AppLayout = lazy(() => import('./components/layout/AppLayout'));

// Import admin routes
import { adminRoutes } from './pages/admin/routes.tsx';

// Create a router with essential routes and app pages
const router = createBrowserRouter([
  // Admin routes
  ...adminRoutes,
  // Auth routes (outside of main layout)
  {
    path: '/auth',
    element: <Suspense fallback={<LoadingSpinner />}><SimpleAuth /></Suspense>,
  },
  // Auth callback route for handling Supabase redirects
  {
    path: '/auth/callback',
    element: <Suspense fallback={<LoadingSpinner />}><AuthCallback /></Suspense>,
  },
  // Redirect /login to /auth for consistency
  {
    path: '/login',
    element: <Navigate to="/auth" replace />,
  },
  // Debug routes - only in development
  ...(import.meta.env.DEV ? [
    {
      path: '/debug/supabase',
      element: SupabaseTest ? <Suspense fallback={<LoadingSpinner />}><SupabaseTest /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/client-test',
      element: ClientTest ? <Suspense fallback={<LoadingSpinner />}><ClientTest /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/error-demo',
      element: ErrorDemo ? <Suspense fallback={<LoadingSpinner />}><ErrorDemo /></Suspense> : <NotFound />,
    },
    {
      path: '/debug/retry-demo',
      element: RetryDemo ? <Suspense fallback={<LoadingSpinner />}><RetryDemo /></Suspense> : <NotFound />,
    },
  ] : []),

  // Main app routes with AppLayout
  {
    element: <Suspense fallback={<LoadingSpinner />}><AppLayout /></Suspense>,
    children: [
      // Home route - Smart routing between public landing and authenticated dashboard
      {
        path: '/',
        element: <Suspense fallback={<LoadingSpinner />}><SmartHome /></Suspense>,
      },
      // Protected routes
      {
        path: '/profile',
        element: (
          <ProtectedRoute>
            <Suspense fallback={<LoadingSpinner />}>
              <Profile />
            </Suspense>
          </ProtectedRoute>
        ),
      },
      {
        path: '/activities',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Activities />
          </Suspense>
        ),
      },
      {
        path: '/famhub',
        element: (
          <ProtectedRoute>
            <Suspense fallback={<LoadingSpinner />}>
              <FamHub />
            </Suspense>
          </ProtectedRoute>
        ),
      },
      {
        path: '/discover',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Discover />
          </Suspense>
        ),
      },
      {
        path: '/emergency',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Emergency />
          </Suspense>
        ),
      },
      {
        path: '/help',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Help />
          </Suspense>
        ),
      },
      {
        path: '/resources',
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Resources />
          </Suspense>
        ),
      },
    ]
  },

  // Catch-all route
  {
    path: '*',
    element: <NotFound />
  }
], {
  future: {
    v7_relativeSplatPath: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
    // v7_startTransition: true // Commented out as not supported in current version
  }
});

// Get root element
const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Root element not found');

// Render the app with providers and error boundary
ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <SecurityProvider>
      <GlobalErrorBoundary>
        <ThemeProvider>
          <ConsolidatedAuthProvider>
            <QueryProvider>
              <RouterProvider router={router} />
              <Analytics />
              {import.meta.env.DEV && (
                <ConnectionStatus />
              )}
            </QueryProvider>
          </ConsolidatedAuthProvider>
        </ThemeProvider>
      </GlobalErrorBoundary>
    </SecurityProvider>
  </React.StrictMode>,
)
