import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '@/hooks/useProfile';
import { supabase } from '@/lib/supabase';
import { isAdminRole } from '@/lib/utils/auth';
import { type Guide } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { MoreVertical, Pencil, Trash, BookOpen } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

const categoryColors = {
  SAFETY: 'bg-red-500/20 text-red-400 hover:bg-red-500/30',
  PACKING: 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30',
  CAMPING: 'bg-green-500/20 text-green-400 hover:bg-green-500/30',
  FOOD: 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30',
  TRANSPORT: 'bg-purple-500/20 text-purple-400 hover:bg-purple-500/30',
  OTHER: 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30',
};

const Guides: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [guides, setGuides] = React.useState<Guide[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    loadGuides();
  }, []);

  React.useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  const loadGuides = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('guides')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setGuides(data || []);
    } catch (error) {
      console.error('Error loading guides:', error);
      setError('Failed to load guides');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this guide? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('guides')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setGuides(guides.filter(g => g.id !== id));
    } catch (error) {
      console.error('Error deleting guide:', error);
      setError('Failed to delete guide');
    }
  };

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Guides</h1>
          <p className="text-muted-foreground mt-2">
            Manage festival guides and resources
          </p>
        </div>
        <Button
          onClick={() => navigate('/admin/guides/new')}
          className="bg-primary/20 hover:bg-primary/30"
        >
          Create Guide
        </Button>
      </div>

      {error && (
        <div className="rounded-lg border border-red-500/20 p-4 text-red-400 bg-red-500/10">
          {error}
        </div>
      )}

      {loading ? (
        <div className="text-center py-8 text-muted-foreground">
          Loading guides...
        </div>
      ) : guides.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No guides found</CardTitle>
            <CardDescription>
              Get started by creating your first guide
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className="grid gap-6">
          {guides.map((guide) => (
            <Card key={guide.id}>
              <CardHeader className="flex flex-row items-start justify-between space-y-0">
                <div className="space-y-1">
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    {guide.title}
                  </CardTitle>
                  {guide.description && (
                    <CardDescription>{guide.description}</CardDescription>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => navigate(`/admin/guides/${guide.id}/edit`)}
                    >
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-400"
                      onClick={() => handleDelete(guide.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Badge
                      variant="secondary"
                      className={cn(categoryColors[guide.category as keyof typeof categoryColors] || categoryColors.OTHER)}
                    >
                      {guide.category}
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      Last updated {guide.updated_at ? format(new Date(guide.updated_at), 'PPP') : 'Never'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default Guides;
