import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { type AppConfig } from '../types/app';

interface UIState {
  isHamburgerMenuOpen: boolean;
  announcementDismissals: Record<string, number>;
  theme: AppConfig['theme'];
  isQuickActionsOpen: boolean;
  toggleHamburgerMenu: () => void;
  toggleQuickActions: () => void;
  dismissAnnouncement: (id: string, hours: number) => void;
  setTheme: (theme: AppConfig['theme']) => void;
}

// Create and export the store
const useUIStore = create<UIState>()(
  persist(
    (set) => ({
      isHamburgerMenuOpen: false,
      announcementDismissals: {},
      theme: {
        primary: '#6366f1',
        secondary: '#4f46e5',
      },
      isQuickActionsOpen: false,
      toggleHamburgerMenu: () =>
        set((state) => ({
          isHamburgerMenuOpen: !state.isHamburgerMenuOpen
        })),
      toggleQuickActions: () =>
        set((state) => ({
          isQuickActionsOpen: !state.isQuickActionsOpen
        })),
      dismissAnnouncement: (id: string, hours: number) => 
        set((state) => ({
          announcementDismissals: {
            ...state.announcementDismissals,
            [id]: Date.now() + (hours * 60 * 60 * 1000)
          }
        })),
      setTheme: (theme: AppConfig['theme']) => 
        set({ theme })
    }),
    {
      name: 'ui-store',
      version: 1,
    }
  )
);

// Export both as named and default export
export { useUIStore };
export default useUIStore;