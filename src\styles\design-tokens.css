/*
 * Festival Family Design System 2025 - Consolidated Extension Layer
 *
 * This file extends the shadcn/ui foundation with minimal Festival Family-specific variables.
 *
 * ARCHITECTURE:
 * - Foundation: shadcn/ui CSS variables (src/index.css) - Single source of truth
 * - Extension: Festival Family semantic variables that reference shadcn/ui foundation
 *
 * USAGE:
 * - Use shadcn/ui variables directly: hsl(var(--primary)), hsl(var(--background))
 * - Use Festival extensions for specific semantics: var(--festival-priority-high)
 */

:root {
  /* === FESTIVAL FAMILY SEMANTIC EXTENSIONS === */
  /* These extend shadcn/ui foundation variables with Festival-specific semantics */

  /* Festival Brand Colors - Reference shadcn/ui foundation */
  --festival-primary: hsl(var(--primary)); /* Festival purple */
  --festival-secondary: hsl(var(--secondary)); /* <PERSON><PERSON> */
  --festival-accent: hsl(var(--accent)); /* Festival orange */

  /* Festival Background Semantics - Reference shadcn/ui foundation */
  --festival-bg: hsl(var(--background)); /* Main background */
  --festival-bg-card: hsl(var(--card)); /* Card backgrounds */
  --festival-bg-muted: hsl(var(--muted)); /* Muted backgrounds */

  /* Festival Text Colors - Reference shadcn/ui foundation */
  --festival-text: hsl(var(--foreground)); /* Main text */
  --festival-text-muted: hsl(var(--muted-foreground)); /* Muted text */
  --festival-text-on-primary: hsl(var(--primary-foreground)); /* Text on primary */
  --festival-text-on-accent: hsl(var(--accent-foreground)); /* Text on accent */

  /* Festival Border Colors - Reference shadcn/ui foundation */
  --festival-border: hsl(var(--border)); /* Standard borders */
  --festival-border-input: hsl(var(--input)); /* Input borders */
  --festival-ring: hsl(var(--ring)); /* Focus rings */

  /* === FESTIVAL FAMILY PRIORITY SYSTEM === */
  /* Priority-based colors for announcements and content categorization */
  /* These reference shadcn/ui semantic colors for consistency */

  --festival-priority-high: hsl(var(--destructive)); /* High priority - Red */
  --festival-priority-high-bg: hsl(var(--destructive) / 0.1); /* High priority background */
  --festival-priority-high-text: hsl(var(--destructive-foreground)); /* High priority text */

  --festival-priority-medium: hsl(var(--accent)); /* Medium priority - Orange */
  --festival-priority-medium-bg: hsl(var(--accent) / 0.1); /* Medium priority background */
  --festival-priority-medium-text: hsl(var(--accent-foreground)); /* Medium priority text */

  --festival-priority-low: hsl(var(--primary)); /* Low priority - Purple */
  --festival-priority-low-bg: hsl(var(--primary) / 0.1); /* Low priority background */
  --festival-priority-low-text: hsl(var(--primary-foreground)); /* Low priority text */

  /* === FESTIVAL FAMILY COMPONENT SEMANTICS === */
  /* Component-specific semantic variables that reference shadcn/ui foundation */

  --festival-success: hsl(142 76% 36%); /* Success green */
  --festival-success-bg: hsl(142 76% 36% / 0.1); /* Success background */
  --festival-success-text: hsl(0 0% 100%); /* Success text */

  --festival-warning: hsl(38 92% 50%); /* Warning amber */
  --festival-warning-bg: hsl(38 92% 50% / 0.1); /* Warning background */
  --festival-warning-text: hsl(0 0% 100%); /* Warning text */

  --festival-error: hsl(var(--destructive)); /* Error - Reference shadcn/ui */
  --festival-error-bg: hsl(var(--destructive) / 0.1); /* Error background */
  --festival-error-text: hsl(var(--destructive-foreground)); /* Error text */

  /* === FESTIVAL FAMILY COMPONENT UTILITIES === */
  /* Utility variables for consistent component styling */

  --festival-radius: var(--radius); /* Reference shadcn/ui radius */
  --festival-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --festival-transition: all 0.2s ease-in-out;

  /* === FESTIVAL FAMILY GRADIENTS === */
  /* Subtle gradients that reference shadcn/ui foundation colors */
  --festival-gradient: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  --festival-gradient-subtle: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);
}

/* === FESTIVAL FAMILY UTILITY CLASSES === */
/* Utility classes that reference the consolidated shadcn/ui foundation */

/* Festival Text Utilities */
.festival-text-gradient {
  background: var(--festival-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.festival-text { color: var(--festival-text); }
.festival-text-muted { color: var(--festival-text-muted); }
.festival-text-on-primary { color: var(--festival-text-on-primary); }
.festival-text-on-accent { color: var(--festival-text-on-accent); }

/* Festival Background Utilities */
.festival-bg { background-color: var(--festival-bg); }
.festival-bg-card { background-color: var(--festival-bg-card); }
.festival-bg-muted { background-color: var(--festival-bg-muted); }
.festival-gradient-bg { background: var(--festival-gradient); }
.festival-gradient-subtle-bg { background: var(--festival-gradient-subtle); }

/* Festival Priority Utilities */
.festival-priority-high {
  color: var(--festival-priority-high-text);
  background-color: var(--festival-priority-high-bg);
  border-color: var(--festival-priority-high);
}
.festival-priority-medium {
  color: var(--festival-priority-medium-text);
  background-color: var(--festival-priority-medium-bg);
  border-color: var(--festival-priority-medium);
}
.festival-priority-low {
  color: var(--festival-priority-low-text);
  background-color: var(--festival-priority-low-bg);
  border-color: var(--festival-priority-low);
}

/* Festival Component Utilities */
.festival-component {
  border-radius: var(--festival-radius);
  box-shadow: var(--festival-shadow);
  transition: var(--festival-transition);
}
