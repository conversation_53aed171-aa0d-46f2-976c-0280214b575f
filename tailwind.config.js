/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Festival Family 2025 - Consolidated with shadcn/ui Foundation
        // These extend the shadcn/ui color system with Festival-specific semantics

        // Festival Priority Colors (for announcements and content categorization)
        'festival-priority-high': 'var(--festival-priority-high)',
        'festival-priority-medium': 'var(--festival-priority-medium)',
        'festival-priority-low': 'var(--festival-priority-low)',

        // Festival Semantic Colors
        'festival-success': 'var(--festival-success)',
        'festival-warning': 'var(--festival-warning)',
        'festival-error': 'var(--festival-error)',

        // Festival Brand Colors (reference shadcn/ui foundation)
        'festival-primary': 'var(--festival-primary)',
        'festival-secondary': 'var(--festival-secondary)',
        'festival-accent': 'var(--festival-accent)',
      },
      fontFamily: {
        'manrope': ['Manrope Variable', 'sans-serif'],
        'outfit': ['Outfit Variable', 'sans-serif'],
        'space-grotesk': ['Space Grotesk', 'sans-serif'],
      },
      backgroundImage: {
        'festival-gradient': 'var(--festival-gradient)',
        'festival-gradient-subtle': 'var(--festival-gradient-subtle)',
      },
      boxShadow: {
        'festival': 'var(--festival-shadow)',
        'festival-glow': '0 0 20px hsl(var(--primary) / 0.3)',
      },
      animation: {
        'slide-in': 'slide-in 0.3s ease-out',
        'slide-out': 'slide-out 0.3s ease-in',
        'fade-in': 'fade-in 0.3s ease-out',
        'fade-out': 'fade-out 0.3s ease-in',
        'bounce-subtle': 'bounce-subtle 2s infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'gradient': 'gradient 15s ease infinite',
      },
      keyframes: {
        'slide-in': {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        'slide-out': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'bounce-subtle': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
        'gradient': {
          '0%, 100%': { 'background-position': '0% 50%' },
          '50%': { 'background-position': '100% 50%' },
        },
      },
      borderRadius: {
        'festival': '1rem',  // More rounded corners
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar-hide'),
  ],
}
